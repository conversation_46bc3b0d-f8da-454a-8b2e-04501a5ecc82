[package]
name = "fisheye-tools"
version = "0.3.1"
edition = "2021"
authors = ["<PERSON><PERSON>"]
description = "A comprehensive Rust library for fisheye and wide-angle camera models with factrs-based optimization"
license = "MIT"
repository = "https://github.com/username/fisheye-tools"
homepage = "https://github.com/username/fisheye-tools"
documentation = "https://docs.rs/fisheye-tools"
keywords = ["camera", "fisheye", "computer-vision", "calibration", "optimization"]
categories = ["computer-vision", "science", "algorithms"]
readme = "README.md"
exclude = [
    "logs/*",
    "output/*",
    "target/*",
    ".github/*"
]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
# Core dependencies
nalgebra = "0.33.2"
factrs = "0.2.0"
thiserror = "2.0.12"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_yaml = "0.9"
serde_json = "1.0"
yaml-rust = "0.4"

# Logging
log = "0.4"

[dev-dependencies]
# Testing dependencies
approx = "0.5.1"

# Example dependencies
clap = { version = "4.5.38", features = ["derive"] }
flexi_logger = "0.30"
env_logger = "0.11"

[profile.release]
# Optimize for performance
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
# Faster compilation for development
opt-level = 0
debug = true

[profile.test]
# Optimize tests for better performance
opt-level = 2
