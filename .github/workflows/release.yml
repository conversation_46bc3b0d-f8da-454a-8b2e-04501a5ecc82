name: Release

on:
  push:
    tags:
      - 'v*'

env:
  CARGO_TERM_COLOR: always

jobs:
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
    steps:
    - name: Create Release
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ github.ref }}
        draft: false
        prerelease: false

  publish-crate:
    name: Publish to crates.io
    runs-on: ubuntu-latest
    needs: create-release
    steps:
    - uses: actions/checkout@v4

    - name: Set up Rust
      uses: dtolnay/rust-toolchain@stable

    - name: Cache Cargo registry
      uses: actions/cache@v4
      with:
        path: ~/.cargo/registry
        key: ${{ runner.os }}-cargo-registry-${{ hashFiles('**/Cargo.lock') }}
        restore-keys: |
          ${{ runner.os }}-cargo-registry-

    - name: Check formatting
      run: cargo fmt --all --check

    - name: Run clippy
      run: cargo clippy --all-targets --all-features -- -D warnings

    - name: Run tests
      run: cargo test --all-features

    - name: Publish to crates.io
      run: cargo publish --token ${{ secrets.CARGO_REGISTRY_TOKEN }}

  build-binaries:
    name: Build Release Binaries
    runs-on: ${{ matrix.os }}
    needs: create-release
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            target: x86_64-unknown-linux-gnu
            artifact_name: fisheye-tools-linux-x86_64
          - os: macos-latest
            target: x86_64-apple-darwin
            artifact_name: fisheye-tools-macos-x86_64
          - os: macos-latest
            target: aarch64-apple-darwin
            artifact_name: fisheye-tools-macos-aarch64
          - os: windows-latest
            target: x86_64-pc-windows-msvc
            artifact_name: fisheye-tools-windows-x86_64.exe

    steps:
    - uses: actions/checkout@v4

    - name: Set up Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        targets: ${{ matrix.target }}

    - name: Build release binary
      run: cargo build --release --target ${{ matrix.target }} --example camera_model_conversion

    - name: Upload Release Asset
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: ./target/${{ matrix.target }}/release/examples/camera_model_conversion${{ matrix.os == 'windows-latest' && '.exe' || '' }}
        asset_name: ${{ matrix.artifact_name }}
        asset_content_type: application/octet-stream
